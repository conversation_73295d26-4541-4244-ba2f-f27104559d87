# First Born Society

Een responsieve, Nederlandse one-page website voor First Born Society - creativiteit en community voor eerstgeborenen.

## ✨ Features

- **Modern Design**: Schone, responsieve interface met een warme kleurpalette
- **Framer Motion Animaties**: Soepele animaties en hover effecten
- **Nederlandse Content**: Volledig in het Nederlands
- **SEO Optimized**: Juiste meta tags en semantische HTML
- **Mobile First**: Responsive design voor alle apparaten
- **Performance**: Geoptimaliseerd met Next.js 15 en Turbopack

## 🎨 Design System

- **Primary Gradient**: `#f3eded` → `#fcb1ab`
- **Accent Color**: `#5c3d24` (dark brown)
- **Typography**: Playfair Display (headings) + Inter (body)
- **Components**: Header, Hero, Mission, Services, Footer

## 🚀 Setup Instructions

### Vereisten
- Node.js 18+ 
- npm of pnpm

### Installatie

```bash
# Clone het project
git clone <repository-url>
cd FirstBorn

# Installeer dependencies
npm install
# of
pnpm install

# Start de development server
npm run dev
# of  
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) in je browser.

### Build voor productie

```bash
npm run build
npm run start
```

## 📁 Project Structuur

```
src/
├── app/
│   ├── layout.tsx        # Root layout met fonts & SEO
│   ├── page.tsx          # Homepage met alle sectie
│   └── globals.css       # Global styles & theme
├── components/
│   ├── Header.tsx        # Fixed header met navigatie
│   ├── Hero.tsx          # Hero sectie met CTA
│   ├── Mission.tsx       # Missie sectie
│   ├── Services.tsx      # Diensten grid
│   └── Footer.tsx        # Footer met contact info
└── assets/
    └── logo.png          # First Born Society logo
```

## 🛠 Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Styling**: Tailwind CSS v4
- **Animations**: Framer Motion
- **Fonts**: Google Fonts (Playfair Display, Inter)
- **Language**: TypeScript
- **Icons**: Emoji + Lucide React

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px  
- **Desktop**: > 1024px

## 🎯 Sections

1. **Header** - Fixed navigatie met logo
2. **Hero** - Welkomstbericht met floating animations
3. **Mission** - Onze missie in twee kolommen
4. **Services** - Drie feature cards met hover effecten
5. **Footer** - Contact informatie en newsletter signup

## 🌟 Animaties

- Scroll-triggered viewport animations
- Hover effects op cards en buttons
- Smooth scroll tussen sectie
- Loading animations met staggered delays

---

**© 2024 First Born Society – Alle rechten voorbehouden.**
