import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import Header from '../Header'

// Mock getElementById for scrollToSection functionality
const mockScrollIntoView = jest.fn()
const mockGetElementById = jest.fn()

beforeEach(() => {
  mockScrollIntoView.mockClear()
  mockGetElementById.mockClear()
  
  // Mock document.getElementById
  document.getElementById = mockGetElementById.mockImplementation((id) => ({
    scrollIntoView: mockScrollIntoView,
  }))
  
  // Reset body overflow style
  document.body.style.overflow = 'unset'
})

describe('Header Component', () => {
  it('renders the header with logo and navigation', () => {
    render(<Header />)
    
    // Check if header is rendered
    expect(screen.getByRole('banner')).toBeInTheDocument()
    
    // Check if logos are present (there are two - one in header, one in mobile menu)
    expect(screen.getAllByAltText('First Born Society Logo')).toHaveLength(2)
    
    // Check if mobile menu button is present
    expect(screen.getByLabelText('Open navigation menu')).toBeInTheDocument()
  })

  it('has proper sticky positioning', () => {
    render(<Header />)
    const header = screen.getByRole('banner')
    
    expect(header).toHaveClass('fixed', 'top-0', 'left-0', 'w-full')
  })

  describe('Mobile Menu Functionality', () => {
    it('opens mobile menu when hamburger button is clicked', async () => {
      const user = userEvent.setup()
      render(<Header />)
      
      const menuButton = screen.getByLabelText('Open navigation menu')
      
      await user.click(menuButton)
      
      // Check if menu is opened
      expect(screen.getByLabelText('Close navigation menu')).toBeInTheDocument()
      expect(screen.getByRole('dialog')).toBeInTheDocument()
      expect(screen.getByLabelText('Mobile navigation menu')).toBeInTheDocument()
    })

    it('closes mobile menu when close button is clicked', async () => {
      const user = userEvent.setup()
      render(<Header />)
      
      const menuButton = screen.getByLabelText('Open navigation menu')
      await user.click(menuButton)
      
      const closeButton = screen.getByLabelText('Close navigation menu')
      await user.click(closeButton)
      
      expect(screen.getByLabelText('Open navigation menu')).toBeInTheDocument()
    })

    it('closes mobile menu when backdrop is clicked', async () => {
      const user = userEvent.setup()
      render(<Header />)

      const menuButton = screen.getByLabelText('Open navigation menu')
      await user.click(menuButton)

      // Find and click the backdrop (the element with aria-hidden="true")
      const backdrop = screen.getByRole('dialog').previousElementSibling
      if (backdrop) {
        await user.click(backdrop)
      }

      await waitFor(() => {
        expect(screen.getByLabelText('Open navigation menu')).toBeInTheDocument()
      })
    })

    it('closes mobile menu when Escape key is pressed', async () => {
      const user = userEvent.setup()
      render(<Header />)
      
      const menuButton = screen.getByLabelText('Open navigation menu')
      await user.click(menuButton)
      
      await user.keyboard('{Escape}')
      
      expect(screen.getByLabelText('Open navigation menu')).toBeInTheDocument()
    })

    it('prevents body scroll when menu is open', async () => {
      const user = userEvent.setup()
      render(<Header />)
      
      const menuButton = screen.getByLabelText('Open navigation menu')
      await user.click(menuButton)
      
      expect(document.body.style.overflow).toBe('hidden')
    })

    it('restores body scroll when menu is closed', async () => {
      const user = userEvent.setup()
      render(<Header />)
      
      const menuButton = screen.getByLabelText('Open navigation menu')
      await user.click(menuButton)
      
      const closeButton = screen.getByLabelText('Close navigation menu')
      await user.click(closeButton)
      
      expect(document.body.style.overflow).toBe('unset')
    })
  })

  describe('Navigation Functionality', () => {
    it('scrolls to correct section when navigation item is clicked', async () => {
      const user = userEvent.setup()
      render(<Header />)
      
      // Open mobile menu
      const menuButton = screen.getByLabelText('Open navigation menu')
      await user.click(menuButton)
      
      // Click on a navigation item
      const homeButton = screen.getByLabelText('Navigate to home section')
      await user.click(homeButton)
      
      expect(mockGetElementById).toHaveBeenCalledWith('top')
      expect(mockScrollIntoView).toHaveBeenCalledWith({ behavior: 'smooth' })
    })

    it('closes mobile menu when navigation item is clicked', async () => {
      const user = userEvent.setup()
      render(<Header />)
      
      // Open mobile menu
      const menuButton = screen.getByLabelText('Open navigation menu')
      await user.click(menuButton)
      
      // Click on a navigation item
      const homeButton = screen.getByLabelText('Navigate to home section')
      await user.click(homeButton)
      
      expect(screen.getByLabelText('Open navigation menu')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes', async () => {
      const user = userEvent.setup()
      render(<Header />)
      
      const menuButton = screen.getByLabelText('Open navigation menu')
      expect(menuButton).toHaveAttribute('aria-expanded', 'false')
      expect(menuButton).toHaveAttribute('aria-controls', 'mobile-menu')
      
      await user.click(menuButton)
      
      expect(screen.getByLabelText('Close navigation menu')).toHaveAttribute('aria-expanded', 'true')
      expect(screen.getByRole('dialog')).toHaveAttribute('aria-modal', 'true')
    })

    it('has proper touch target sizes', () => {
      render(<Header />)
      
      const menuButton = screen.getByLabelText('Open navigation menu')
      expect(menuButton).toHaveClass('p-2') // Ensures minimum touch target
    })
  })

  describe('Responsive Behavior', () => {
    it('shows mobile menu button only on mobile', () => {
      render(<Header />)
      
      const menuButton = screen.getByLabelText('Open navigation menu')
      expect(menuButton.closest('div')).toHaveClass('md:hidden')
    })

    it('hides mobile menu overlay on desktop', () => {
      render(<Header />)
      
      // The mobile menu overlay should have md:hidden class
      const mobileMenuElements = screen.getAllByText('HOME')
      const mobileMenuButton = mobileMenuElements.find(el => 
        el.closest('[role="dialog"]')
      )
      
      if (mobileMenuButton) {
        expect(mobileMenuButton.closest('[role="dialog"]')).toHaveClass('md:hidden')
      }
    })
  })
})
