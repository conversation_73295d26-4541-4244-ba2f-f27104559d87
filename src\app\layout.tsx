import type { <PERSON>ada<PERSON> } from "next";
import { Playfair_Display, Inter } from "next/font/google";
import "./globals.css";

const playfairDisplay = Playfair_Display({
  variable: "--font-display",
  subsets: ["latin"],
  weight: ["400", "600", "700"],
  display: "swap"
});

const inter = Inter({
  variable: "--font-body",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600"],
  display: "swap"
});

export const metadata: Metadata = {
  title: "First Born Society - Creativiteit en Community voor Eerstgeborenen",
  description: "Welkom bij First Born Society, dé plek waar creativiteit en community samenkomen voor eerstgeborenen. Ontdek onze missie en diensten.",
  keywords: ["first born society", "eerstgeborenen", "creativiteit", "community", "Nederland", "creatieve diensten"],
  authors: [{ name: "First Born Society" }],
  robots: "index, follow",
  openGraph: {
    title: "First Born Society - Creativiteit en Community",
    description: "Creativiteit en community voor eerstgeborenen",
    type: "website",
    locale: "nl_NL"
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="nl" className={`${playfairDisplay.variable} ${inter.variable}`}>
      <body className="antialiased">
        {children}
      </body>
    </html>
  );
}
