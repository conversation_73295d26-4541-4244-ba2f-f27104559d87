'use client'

import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef, useState } from 'react'
import Image from 'next/image'

const galleryImages = [
  {
    src: '/assets/WhatsApp Image 2025-06-28 at 14.40.07.jpeg',
    alt: '<PERSON> en Celena samen - Het perfecte duo',
    title: 'Het Perfecte Duo',
    description: '<PERSON> en Celena verenigd in hun missie'
  },
  {
    src: '/assets/WhatsApp Image 2025-06-28 at 14.40.06.jpeg',
    alt: '<PERSON> <PERSON>verzorgster vol passie',
    title: '<PERSON> in Actie',
    description: '<PERSON><PERSON>am<PERSON><PERSON> met hart en ziel'
  },
  {
    src: '/assets/WhatsApp Image 2025-06-28 at 14.40.04.jpeg',
    alt: 'Community moment - Samen leren en groeien',
    title: 'Community Moment',
    description: 'Samen leren en groeien'
  },
  {
    src: '/assets/WhatsApp Image 2025-06-28 at 14.40.02.jpeg',
    alt: 'Workshop sessie - Creativiteit en verbinding',
    title: 'Workshop Sessie',
    description: 'Creativiteit en verbinding'
  },
  {
    src: '/assets/WhatsApp Image 2025-06-28 at 14.39.37.jpeg',
    alt: 'Inspirerende momenten samen',
    title: 'Inspirerende Momenten',
    description: 'Samen nieuwe wegen bewandelen'
  },
  {
    src: '/assets/WhatsApp Image 2025-06-28 at 14.39.37 (1).jpeg',
    alt: 'Celena - Frisse perspectieven',
    title: 'Frisse Perspectieven',
    description: 'Celena brengt nieuwe inzichten'
  }
]

export default function Gallery() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  const [selectedImage, setSelectedImage] = useState<number | null>(null)

  return (
    <section 
      id="galerij" 
      ref={ref}
      className="py-20 bg-gradient-to-b from-white/60 to-white/40 backdrop-blur-sm relative overflow-hidden"
    >
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={isInView ? { y: 0, opacity: 1 } : { y: 30, opacity: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-display font-bold text-[#5c3d24] mb-6">
            Galerij
          </h2>
          <p className="text-lg text-gray-700 max-w-2xl mx-auto font-body">
            Ontdek de mooie momenten van First Born Society. Van persoonlijke verhalen tot 
            inspirerende bijeenkomsten - zie hoe onze community tot leven komt.
          </p>
        </motion.div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {galleryImages.map((image, index) => (
            <motion.div
              key={index}
              initial={{ y: 50, opacity: 0 }}
              animate={isInView ? { y: 0, opacity: 1 } : { y: 50, opacity: 0 }}
              transition={{ duration: 0.8, delay: index * 0.15 }}
              whileHover={{ scale: 1.03 }}
              onClick={() => setSelectedImage(index)}
              className="relative aspect-square rounded-2xl overflow-hidden shadow-lg border-4 border-white hover:border-[#fcb1ab] transition-all duration-300 cursor-pointer group"
            >
              <Image
                src={image.src}
                alt={image.alt}
                fill
                className="object-cover transition-transform duration-500 group-hover:scale-110"
              />
              
              {/* Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-[#5c3d24]/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              
              {/* Image Info */}
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                <h3 className="font-display font-bold text-lg mb-1">
                  {image.title}
                </h3>
                <p className="text-white/90 font-body text-sm">
                  {image.description}
                </p>
              </div>

              {/* Hover Icon */}
              <div className="absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                </svg>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={isInView ? { y: 0, opacity: 1 } : { y: 30, opacity: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="text-center mt-16"
        >
          <p className="text-lg text-gray-700 mb-6 font-body">
            Wil jij ook deel uitmaken van deze mooie momenten?
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-gradient-to-r from-[#5c3d24] to-[#8b5a3c] text-white px-8 py-4 rounded-full font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300"
          >
            Neem Contact Op
          </motion.button>
        </motion.div>
      </div>

      {/* Modal for Enlarged Image */}
      {selectedImage !== null && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            className="relative max-w-4xl max-h-[90vh] w-full"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="relative aspect-square rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src={galleryImages[selectedImage].src}
                alt={galleryImages[selectedImage].alt}
                fill
                className="object-cover"
              />
            </div>
            
            {/* Image Info in Modal */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6 rounded-b-2xl">
              <h3 className="font-display font-bold text-2xl text-white mb-2">
                {galleryImages[selectedImage].title}
              </h3>
              <p className="text-white/90 font-body text-lg">
                {galleryImages[selectedImage].description}
              </p>
            </div>

            {/* Close Button */}
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute top-4 right-4 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors duration-200"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Navigation Arrows */}
            {selectedImage > 0 && (
              <button
                onClick={() => setSelectedImage(selectedImage - 1)}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors duration-200"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
            )}
            
            {selectedImage < galleryImages.length - 1 && (
              <button
                onClick={() => setSelectedImage(selectedImage + 1)}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors duration-200"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            )}
          </motion.div>
        </motion.div>
      )}
    </section>
  )
} 