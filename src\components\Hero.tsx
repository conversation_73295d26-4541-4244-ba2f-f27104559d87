'use client'

import { motion } from 'framer-motion'

export default function Hero() {
  return (
    <section 
      id="hero" 
      className="h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-b from-[#f3eded] to-[#fcb1ab]"
    >


      {/* Main Content */}
      <div className="container mx-auto px-4 md:px-6 text-center z-10 relative mt-16 sm:mt-20 md:mt-24 lg:mt-0">
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 1.2, delay: 0.3 }}
          className="relative"
        >
          {/* Main text with beautiful gradient effect */}
          <motion.h1 
            className="relative text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl 2xl:text-9xl font-display font-bold leading-tight mb-6 sm:mb-8 md:mb-12"
            initial={{ 
              backgroundPosition: "0% 50%",
            }}
            animate={{ 
              backgroundPosition: "100% 50%",
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }}
            style={{
              background: "linear-gradient(90deg, #5c3d24, #fcb1ab, #f3eded, #8b5a3c, #5c3d24)",
              backgroundSize: "300% 100%",
              backgroundClip: "text",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              filter: "drop-shadow(2px 2px 4px rgba(92, 61, 36, 0.3))"
            }}
          >
            First Born Society
          </motion.h1>

          {/* Elegant underline */}
          <motion.div
            className="mx-auto"
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: "200px", opacity: 1 }}
            transition={{ duration: 1.5, delay: 1 }}
          >
            <div className="h-0.5 bg-gradient-to-r from-transparent via-[#5c3d24] to-transparent" />
          </motion.div>

          {/* Subtitle */}
          <motion.p
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 1, delay: 1.5 }}
            className="text-[#5c3d24]/80 font-body text-lg md:text-xl lg:text-2xl mt-8 max-w-2xl mx-auto"
          >
            Waar eerstgeborenen samenkomen om te leiden, creëren en groeien
          </motion.p>
        </motion.div>
      </div>
    </section>
  )
} 