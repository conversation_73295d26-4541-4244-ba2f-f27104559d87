'use client'

import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'

export default function VideoIntro() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  return (
    <section 
      id="introductie" 
      ref={ref}
      className="py-20 bg-gradient-to-b from-white/80 to-white/60 backdrop-blur-sm relative overflow-hidden"
    >


      <div className="container mx-auto px-6">
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={isInView ? { y: 0, opacity: 1 } : { y: 30, opacity: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-display font-bold text-[#5c3d24] mb-6">
            Ontdek On<PERSON> Wereld
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto font-body leading-relaxed">
            <PERSON><PERSON> in de levendige wereld van First Born Society. Deze introductie toont je 
            wie we zijn, wat we doen en hoe onze community van eerstgeborenen samen groeit, 
            leert en inspireert.
          </p>
        </motion.div>

        {/* Main Video Section */}
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={isInView ? { y: 0, opacity: 1 } : { y: 50, opacity: 0 }}
          transition={{ duration: 1, delay: 0.3 }}
          className="relative max-w-6xl mx-auto"
        >
          {/* Video Container with decorative frame */}
          <div className="relative bg-gradient-to-br from-[#5c3d24] to-[#8b5a3c] p-4 md:p-8 rounded-3xl shadow-2xl">
            <div className="relative aspect-video rounded-2xl overflow-hidden shadow-xl">
              <video
                className="w-full h-full object-cover"
                controls
                poster="/assets/WhatsApp Image 2025-06-28 at 14.40.02.jpeg"
              >
                <source src="/assets/WhatsApp Video 2025-06-28 at 14.40.01.mp4" type="video/mp4" />
                Je browser ondersteunt geen video weergave.
              </video>
              
              {/* Video overlay for before play */}
              <div className="absolute inset-0 bg-gradient-to-t from-[#5c3d24]/20 via-transparent to-transparent pointer-events-none" />
            </div>

            {/* Decorative corners */}
            <div className="absolute -top-2 -left-2 w-8 h-8 border-t-4 border-l-4 border-[#fcb1ab] rounded-tl-lg"></div>
            <div className="absolute -top-2 -right-2 w-8 h-8 border-t-4 border-r-4 border-[#fcb1ab] rounded-tr-lg"></div>
            <div className="absolute -bottom-2 -left-2 w-8 h-8 border-b-4 border-l-4 border-[#fcb1ab] rounded-bl-lg"></div>
            <div className="absolute -bottom-2 -right-2 w-8 h-8 border-b-4 border-r-4 border-[#fcb1ab] rounded-br-lg"></div>
          </div>


        </motion.div>

        {/* Video Description */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={isInView ? { y: 0, opacity: 1 } : { y: 30, opacity: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16 max-w-4xl mx-auto"
        >
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 shadow-lg">
              <div className="w-12 h-12 bg-[#5c3d24] rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                  <path d="M6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                </svg>
              </div>
              <h3 className="font-display font-semibold text-[#5c3d24] mb-2">Onze Community</h3>
              <p className="text-gray-700 font-body text-sm">
                Zie hoe eerstgeborenen uit verschillende achtergronden samenkomen
              </p>
            </div>

            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 shadow-lg">
              <div className="w-12 h-12 bg-[#5c3d24] rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="font-display font-semibold text-[#5c3d24] mb-2">Activiteiten</h3>
              <p className="text-gray-700 font-body text-sm">
                Van creatieve workshops tot leadership development sessies
              </p>
            </div>

            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 shadow-lg">
              <div className="w-12 h-12 bg-[#5c3d24] rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="font-display font-semibold text-[#5c3d24] mb-2">Groei</h3>
              <p className="text-gray-700 font-body text-sm">
                Persoonlijke en professionele ontwikkeling hand in hand
              </p>
            </div>
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={isInView ? { y: 0, opacity: 1 } : { y: 30, opacity: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="text-center mt-16"
        >
          <p className="text-lg text-gray-700 mb-6 font-body">
            Geïnspireerd door wat je ziet? Leer Kelly en Celena kennen en ontdek hun verhaal.
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => document.getElementById('over-ons')?.scrollIntoView({ behavior: 'smooth' })}
            className="bg-gradient-to-r from-[#5c3d24] to-[#8b5a3c] text-white px-8 py-4 rounded-full font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300"
          >
            Leer Ons Kennen
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
} 