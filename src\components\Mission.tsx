'use client'

import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import Image from 'next/image'

export default function Mission() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  return (
    <section 
      id="missie" 
      ref={ref}
      className="py-20 bg-white/60 backdrop-blur-sm relative"
    >


      <div className="container mx-auto px-6">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ x: -50, opacity: 0 }}
            animate={isInView ? { x: 0, opacity: 1 } : { x: -50, opacity: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-display font-bold text-[#5c3d24] mb-6">
              Onze Missie
            </h2>
            
            <div className="space-y-6 text-gray-700 font-body">
              <p className="text-lg leading-relaxed">
                Bij First Born Society geloven we in de kracht van het eerstgeborene perspectief. 
                Als eerstgeborenen dragen we unieke eigenschappen: leiderschap, verantwoordelijkheid, 
                en een natuurlijke neiging om nieuwe wegen te banen.
              </p>
              
              <p className="text-lg leading-relaxed">
                Onze missie is om een platform te creëren waar eerstgeborenen elkaar kunnen vinden, 
                inspireren en samen groeien. We combineren creativiteit met community om 
                betekenisvolle verbindingen en projecten tot leven te brengen.
              </p>
              
              <p className="text-lg leading-relaxed">
                Door tradities te eren en tegelijkertijd innovatie te omarmen, bouwen we aan 
                een community die zowel geaard als visionair is.
              </p>
            </div>

            {/* Key Values */}
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={isInView ? { y: 0, opacity: 1 } : { y: 30, opacity: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="mt-8 grid grid-cols-3 gap-6"
            >
              <div className="text-center">
                <div className="w-16 h-16 bg-[#5c3d24] rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="font-display font-semibold text-[#5c3d24]">Creativiteit</h3>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-[#5c3d24] rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                    <path d="M6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                  </svg>
                </div>
                <h3 className="font-display font-semibold text-[#5c3d24]">Community</h3>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-[#5c3d24] rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M9.504 1.132a1 1 0 01.992 0l1.75 1a1 1 0 11-.992 1.736L10 3.152l-1.254.716a1 1 0 11-.992-1.736l1.75-1zM5.618 4.504a1 1 0 01-.372 1.364L5.016 6l.23.132a1 1 0 11-.992 1.736L4 7.723V8a1 1 0 01-2 0V6a.996.996 0 01.52-.878l1.734-.99a1 1 0 011.364.372zm8.764 0a1 1 0 011.364-.372l1.733.99A1.002 1.002 0 0118 6v2a1 1 0 11-2 0v-.277l-.254.145a1 1 0 11-.992-1.736l.23-.132-.23-.132a1 1 0 01-.372-1.364zm-7 4a1 1 0 011.364-.372L10 8.848l1.254-.716a1 1 0 11.992 1.736L11 10.587V12a1 1 0 11-2 0v-1.413l-1.246-.72a1 1 0 01-.372-1.363z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="font-display font-semibold text-[#5c3d24]">Leiderschap</h3>
              </div>
            </motion.div>
          </motion.div>

          {/* Real Image from Assets */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={isInView ? { x: 0, opacity: 1 } : { x: 50, opacity: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="relative aspect-square rounded-2xl shadow-2xl overflow-hidden group">
              <Image
                src="/assets/WhatsApp Image 2025-06-28 at 14.39.37.jpeg"
                alt="First Born Society Community Moment"
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-[#5c3d24]/40 via-transparent to-transparent" />
              <div className="absolute bottom-6 left-6 right-6 text-white">
                <h3 className="font-display font-semibold text-xl mb-2">Onze Community</h3>
                <p className="text-white/90 font-body">
                  Samen sterker: eerstgeborenen die elkaar inspireren en ondersteunen
                </p>
              </div>
            </div>


          </motion.div>
        </div>
      </div>
    </section>
  )
} 