@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600&display=swap');

:root {
  /* First Born Society Color Palette */
  --primary-light: #f3eded;
  --primary-dark: #fcb1ab;
  --accent-brown: #5c3d24;
  --background: #ffffff;
  --foreground: #171717;
  
  /* Font families */
  --font-display: "Playfair Display", serif;
  --font-body: "Inter", sans-serif;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

body {
  background: linear-gradient(180deg, var(--primary-light) 0%, var(--primary-dark) 100%);
  background-attachment: fixed;
  color: var(--foreground);
  font-family: var(--font-body);
  line-height: 1.6;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--primary-light);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-brown);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4a2e1a;
}
