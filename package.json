{"name": "firstborn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.19.1", "lucide-react": "^0.523.0", "next": "14.2.13", "react": "^18.2.0", "react-dom": "^18.2.0", "stylus": "^0.64.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^18.2.75", "@types/react-dom": "^18.2.24", "@types/stylus": "^0.48.43", "autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-config-next": "14.2.13", "nuqs": "^2.4.3", "prettier": "^3.6.1", "tailwindcss": "^4", "typescript": "^5"}}