'use client'

import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import Image from 'next/image'

export default function AboutUs() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  return (
    <section 
      id="over-ons" 
      ref={ref}
      className="py-20 bg-gradient-to-b from-white/60 to-white/80 backdrop-blur-sm relative overflow-hidden"
    >


      <div className="container mx-auto px-6">
        {/* Section Header */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={isInView ? { y: 0, opacity: 1 } : { y: 30, opacity: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-display font-bold text-[#5c3d24] mb-6">
            Over Ons
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto font-body leading-relaxed">
            Wij <PERSON><PERSON><PERSON> en <PERSON>lena, twee vriendinnen en buurvrouwen die een idee tot werkelijkheid hebben gemaakt. 
            De droom en ons doel is om zo veel mogelijk vrouwen en gezinnen te kunnen helpen en zo de allerbeste start kunnen bieden.
          </p>
        </motion.div>

        {/* Kelly & Celena Together Image */}
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={isInView ? { y: 0, opacity: 1 } : { y: 50, opacity: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="mb-16 max-w-4xl mx-auto"
        >
          <div className="relative">
            {/* Main Image Container */}
            <div className="relative aspect-[4/3] rounded-3xl overflow-hidden shadow-2xl border-8 border-white">
              <Image
                src="/assets/WhatsApp Image 2025-06-28 at 14.40.07.jpeg"
                alt="Kelly en Celena - Het perfecte duo achter First Born Society"
                fill
                className="object-cover transition-transform duration-300 hover:scale-105"
              />
              {/* Elegant overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-[#5c3d24]/30 via-transparent to-transparent" />
              
              {/* Image Caption */}
              <div className="absolute bottom-6 left-6 right-6 text-white">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                  <h3 className="font-display font-bold text-xl mb-2 text-center">
                    Kelly & Celena
                  </h3>
                  <p className="text-white/90 font-body text-center text-sm">
                    Samen sterk - de kracht achter First Born Society
                  </p>
                </div>
              </div>
            </div>

            {/* Decorative Corner Elements */}
            <div className="absolute -top-4 -left-4 w-8 h-8 bg-gradient-to-br from-[#fcb1ab] to-[#8b5a3c] rounded-full shadow-lg"></div>
            <div className="absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-br from-[#5c3d24] to-[#8b5a3c] rounded-full shadow-lg"></div>
            <div className="absolute -bottom-4 -left-4 w-8 h-8 bg-gradient-to-br from-[#5c3d24] to-[#fcb1ab] rounded-full shadow-lg"></div>
            <div className="absolute -bottom-4 -right-4 w-8 h-8 bg-gradient-to-br from-[#fcb1ab] to-[#5c3d24] rounded-full shadow-lg"></div>
          </div>
        </motion.div>

        {/* Kelly & Celena Grid */}
        <div className="grid lg:grid-cols-2 gap-16 items-stretch">
          {/* Kelly */}
          <motion.div
            initial={{ x: -50, opacity: 0 }}
            animate={isInView ? { x: 0, opacity: 1 } : { x: -50, opacity: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative h-full"
          >
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/20 h-full flex flex-col">
              {/* Kelly's Image */}
              <div className="relative w-32 h-32 mx-auto mb-6">
                <div className="absolute inset-0 bg-gradient-to-br from-[#5c3d24] to-[#8b5a3c] rounded-full p-1">
                  <div className="w-full h-full bg-white rounded-full overflow-hidden">
                    <Image
                      src="/assets/WhatsApp Image 2025-06-28 at 14.40.06.jpeg"
                      alt="Kelly - Kraamverzorgster"
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
              </div>

              <div className="text-center mb-6">
                <h3 className="text-2xl font-display font-bold text-[#5c3d24] mb-2">Kelly</h3>
                <p className="text-[#8b5a3c] font-medium text-lg">Kraamverzorgster in Hart en Nieren</p>
              </div>

              <div className="space-y-4 text-gray-700 font-body flex-1">
                <p className="leading-relaxed">
                  Kelly is zelf al geruime tijd zelfstandig kraamverzorgster in hart en nieren. 
                  Ze doet haar werk met veel passie en zou dan ook nooit meer anders willen.
                </p>
                <p className="leading-relaxed">
                  Samen met haar vriend Sander en haar dochter Jailey heeft ze haar eigen mooie gezin. 
                  Natuurlijk kunnen hondje Daisy en paard Gina ook niet ontbreken.
                </p>
                <p className="leading-relaxed text-[#5c3d24] font-medium">
                  Het mag dan ook duidelijk zijn dat Kelly haar hart sneller gaat kloppen voor de zorg.
                </p>
              </div>
            </div>
          </motion.div>

          {/* Celena */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={isInView ? { x: 0, opacity: 1 } : { x: 50, opacity: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative h-full"
          >
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/20 h-full flex flex-col">
              {/* Celena's Image */}
              <div className="relative w-32 h-32 mx-auto mb-6">
                <div className="absolute inset-0 bg-gradient-to-br from-[#fcb1ab] to-[#8b5a3c] rounded-full p-1">
                  <div className="w-full h-full bg-white rounded-full overflow-hidden">
                    <Image
                      src="/assets/WhatsApp Image 2025-06-28 at 14.39.37 (1).jpeg"
                      alt="Celena - Sales & Paardensport"
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
              </div>

              <div className="text-center mb-6">
                <h3 className="text-2xl font-display font-bold text-[#5c3d24] mb-2">Celena</h3>
                <p className="text-[#8b5a3c] font-medium text-lg">Sales & Paardensport</p>
              </div>

              <div className="space-y-4 text-gray-700 font-body flex-1">
                <p className="leading-relaxed">
                  Celena komt niet uit de kinderen/zorg maar voornamelijk uit de paardensport en sales. 
                  Hierdoor kan ze zich juist verplaatsen in de positie van de nieuwe ouder.
                </p>
                <p className="leading-relaxed italic text-[#5c3d24]">
                  &ldquo;Maar Kelly, ik zou niet weten waar ik moet beginnen wanneer ik zwanger zou zijn. 
                  Je ziet door de bomen het bos niet meer.&rdquo;
                </p>
                <p className="leading-relaxed">
                  Zo is het idee ook ontstaan - door deze frisse blik en het begrijpen van wat nieuwe ouders nodig hebben.
                </p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Partnership Highlight */}
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={isInView ? { y: 0, opacity: 1 } : { y: 50, opacity: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-16 max-w-4xl mx-auto"
        >
          <div className="bg-gradient-to-r from-[#5c3d24] to-[#8b5a3c] rounded-2xl p-8 text-white text-center shadow-xl">
            <h3 className="text-2xl md:text-3xl font-display font-bold mb-4">
              Het Perfecte Duo
            </h3>
            <p className="text-lg font-body leading-relaxed mb-6 text-white/90">
              Juist deze combinatie maakt ons een sterk duo, waardoor de samenwerking vlekkeloos gaat. 
              Kelly&apos;s expertise in zorg en Celena&apos;s perspectief als nieuwe ouder vormen samen de perfecte balans.
            </p>
            
            <div className="grid md:grid-cols-2 gap-6 mt-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                <div className="flex items-center justify-center mb-4">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <h4 className="font-display font-semibold text-lg mb-2">Zorg & Expertise</h4>
                <p className="text-white/80 text-sm">
                  Kelly&apos;s jarenlange ervaring in kraamzorg
                </p>
              </div>
              
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                <div className="flex items-center justify-center mb-4">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <h4 className="font-display font-semibold text-lg mb-2">Perspectief & Inzicht</h4>
                <p className="text-white/80 text-sm">
                  Celena&apos;s frisse blik vanuit de ouder-ervaring
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
} 